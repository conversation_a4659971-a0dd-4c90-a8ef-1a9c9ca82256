"use client";
// use-roadview.ts
import { Draw, Event, <PERSON><PERSON>, MarkerEventHandlers } from "@geon-map/core";
import { useMapStore } from "@geon-map/react-odf";
import { useState } from "react";

import MapMarkerIcon from "../resource/images/map-marker.png";

interface UseRoadviewProps {
  onMarkerSelect: (marker: any) => void;
  onCenterSelect?: (center: [number, number]) => void;
}

export function useRoadview({
  onMarkerSelect,
  onCenterSelect,
}: UseRoadviewProps) {
  const [enabled, setEnabled] = useState(false);
  const [resizableWidth, setResizableWidth] = useState(160);
  const [prevWidth, setPrevWidth] = useState(420);
  const [roadviewLayer, setRoadviewLayer] = useState<any | null>(null);
  const odf = useMapStore((state) => state.odf);
  const map = useMapStore((state) => state.map);
  const DrawInstance = Draw.getInstance();

  const roadviewLayerOption = {
    service: "xyz",
    projection: "EPSG:5181",
    extent: [-30000, -60000, 494288, 988576],
    tileGrid: {
      origin: [-30000, -60000],
      resolutions: [
        4096, 2048, 1024, 512, 256, 128, 64, 32, 16, 8, 4, 2, 1, 0.5, 0.25,
        0.125,
      ],
      matrixIds: [
        "0",
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        "10",
        "11",
        "12",
        "13",
        "14",
        "15",
      ],
    },
    server: {
      url: "/api/daum/map_k3f_prod/bakery/image_map_png/PNGSD_RV01/v16_o9kb2/{{15-z}}/{{-y-1}}/{{x}}.png",
    },
  };

  const enableRoadView = () => {
    setEnabled(true);
    setResizableWidth(prevWidth);
  };

  const disableRoadView = () => {
    setEnabled(false);
    setPrevWidth(resizableWidth);
    setResizableWidth(160);
    map?.removeLayer(roadviewLayer);
  };

  const roadviewLayerOn = () => {
    if (odf && map) {
      const layer = odf.LayerFactory.produce("api", roadviewLayerOption);
      layer.setOpacity(0.5);
      layer.setMap(map);
      setRoadviewLayer(layer);
    }
  };

  const handlers: MarkerEventHandlers = {
    onDragEnd: (event) => {
      const newPosition: { _x: number; _y: number } = event.getPosition();
      map?.setCenter(newPosition);
      onCenterSelect?.([newPosition._x, newPosition._y]);
    },
  };

  const handleToggle = (checked: boolean, marker: any) => {
    if (checked) {
      roadviewLayerOn();
    }

    if (odf && map && checked) {
      const eventInstance = Event.getInstance(map, odf);
      alert("지도에 지점을 선택해주세요");

      DrawInstance.pointCompleted().completed((feature, eventId) => {
        const centerPoint = feature.getCenterPoint();
        onCenterSelect?.(centerPoint);

        const markerObj = Marker.createAndAddMarker(
          map,
          {
            position: new odf.Coordinate(centerPoint),
            draggable: true,
            style: {
              src: MapMarkerIcon.src,
              height: "50px",
              width: "50px",
            },
          },
          handlers,
        ).odfMarker;

        onMarkerSelect(markerObj);
        enableRoadView();

        eventInstance.removeListener(eventId);
        eventInstance.addListener(
          "postcompose",
          () => {
            DrawInstance.deleteFeature(feature);
          },
          true,
        );
      });
    } else {
      Marker.removeMarker(marker);
      onMarkerSelect(null);
      disableRoadView();
    }
  };

  return {
    enabled,
    resizableWidth,
    setResizableWidth,
    prevWidth,
    setPrevWidth,
    handleToggle,
  };
}
