import NextBundleAnalyzer from "@next/bundle-analyzer";

/** @type {import('next').NextConfig} */
const nextConfig = {
  turbopack: {
    rules: {
      // svg loader
      "*.svg": {
        loaders: ["@svgr/webpack"],
      },
    },
  },
  webpack(config, options) {
    // svg loader
    config.module.rules.push({
      test: /\.(png|jpe?g|gif|svg)$/i,
      use: ["@svgr/webpack"],
    });
    // source map 을 dev 실행시에만 활성화
    if (options.dev) {
      config.devTool = options.isServer
        ? false
        : "eval-cheap-module-source-map";
    }
    return config;
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  output: "standalone",
  experimental: {
    optimizePackageImports: ["lucide-react"],
  },
  transpilePackages: [
    "web",
    "@config/*",
    "@geon-ui/react",
    "@geon-query/*",
    "@geon-map/*",
  ],
};

const analyze = process.env.ANALYZE === "true";
const withBundleAnalyze = NextBundleAnalyzer({
  enabled: analyze,
});

export default withBundleAnalyze(nextConfig);
