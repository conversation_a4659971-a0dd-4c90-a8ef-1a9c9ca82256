"use client";

//수정된 사항
import { MapProjection } from "@geon-map/core";
import { useCallback } from "react";

import { useLayerStore } from "../stores/layer-store";
import { useMapStore } from "../stores/map-store";
import type {
  AddFeatureOptions,
  APILayerProps,
  CSVLayerProps,
  GeoJSONLayerProps,
  GeoserverLayerProps,
  GeoTiffLayerProps,
  KMLLayerProps,
  Layer,
  LayerProps,
  LayerType,
  SVGLayerProps,
} from "../types/layer-types";
declare global {
  interface Window {
    odf: any;
  }
}

function convertToLayerParams(props: LayerProps): {
  type: LayerType;
  params: any;
} {
  const {
    type,
    renderOptions,
    className,
    webGLRender,
    attributions,
    children,
    visible,
    zIndex,
    ...rest
  } = props;
  console.log(className, webGLRender, attributions, children, visible, zIndex);

  switch (type) {
    case "empty": {
      // empty 타입은 파라미터가 필요 없음
      return {
        type,
        params: {
          renderOptions,
        },
      };
    }
    case "geoserver": {
      const geoserverProps = rest as GeoserverLayerProps;
      return {
        type,
        params: {
          server:
            typeof geoserverProps.server === "string"
              ? geoserverProps.server
              : geoserverProps.server.url,
          layer: geoserverProps.layer,
          service: geoserverProps.service,
          method: geoserverProps.method || "get",
          bbox: geoserverProps.bbox || false,
          crtfckey: geoserverProps.crtfckey || "",
          projection: geoserverProps.projection || "EPSG:5186",
          limit: geoserverProps.limit,
          tiled: geoserverProps.tiled,
          geometryType: geoserverProps.geometryType,
          serviceTy: geoserverProps.serviceTy,
          ...(typeof geoserverProps.server !== "string" && {
            version: geoserverProps.server.version,
            proxyURL: geoserverProps.server.proxyURL,
            proxyParam: geoserverProps.server.proxyParam,
          }),
        },
      };
    }

    case "geotiff": {
      const geotiffProps = rest as GeoTiffLayerProps;
      return {
        type,
        params: {
          sources: geotiffProps.sources,
          normalize: geotiffProps.normalize ?? true,
          wrapX: geotiffProps.wrapX,
          opaque: geotiffProps.opaque,
          transition: geotiffProps.transition,
          renderOptions,
        },
      };
    }

    case "geojson": {
      const geojsonProps = rest as GeoJSONLayerProps;
      return {
        type,
        params: {
          data: geojsonProps.data,
          dataProjectionCode: geojsonProps.dataProjectionCode,
          featureProjectionCode: geojsonProps.featureProjectionCode,
          service: geojsonProps.service,
          renderOptions,
        },
      };
    }

    case "kml": {
      const kmlProps = rest as KMLLayerProps;
      return {
        type,
        params: {
          data: kmlProps.data,
          dataProjectionCode: kmlProps.dataProjectionCode,
          featureProjectionCode: kmlProps.featureProjectionCode,
          renderOptions,
        },
      };
    }

    case "csv": {
      const csvProps = rest as CSVLayerProps;
      return {
        type,
        params: {
          data: csvProps.data,
          dataProjectionCode: csvProps.dataProjectionCode,
          featureProjectionCode: csvProps.featureProjectionCode,
          geometryColumnName: csvProps.geometryColumnName,
          delimiter: csvProps.delimiter,
          renderOptions,
        },
      };
    }

    case "api": {
      const apiProps = rest as APILayerProps;
      return {
        type,
        params: {
          server:
            typeof apiProps.server === "string"
              ? apiProps.server
              : apiProps.server.url,
          service: apiProps.service,
          bbox: apiProps.bbox,
          tiled: apiProps.tiled,
          tileGrid: apiProps.tileGrid,
          originalOption: apiProps.originalOption,
          parameterFilter: apiProps.parameterFilter,
          ...(typeof apiProps.server !== "string" && {
            proxyURL: apiProps.server.proxyURL,
            proxyParam: apiProps.server.proxyParam,
          }),
          renderOptions,
        },
      };
    }

    case "svg": {
      const svgProps = rest as SVGLayerProps;
      return {
        type,
        params: {
          svgContainer: svgProps.svgContainer,
          extent: svgProps.extent,
          renderOptions,
        },
      };
    }

    default:
      throw new Error(`Unsupported layer type: ${type}`);
  }
}

export function useLayer() {
  const map = useMapStore((state) => state.map);
  const layers = useLayerStore((state) => state.layers);
  const selectedLayerId = useLayerStore((state) => state.selectedLayerId);
  const expandedGroups = useLayerStore((state) => state.expandedGroups);
  const addLayerToStore = useLayerStore((state) => state.addLayer);
  const removeLayerFromStore = useLayerStore((state) => state.removeLayer);
  const updateLayerInStore = useLayerStore((state) => state.updateLayer);
  const setSelectedLayer = useLayerStore((state) => state.setSelectedLayer);
  const toggleGroup = useLayerStore((state) => state.toggleGroup);

  const addLayer = useCallback(
    async (props: LayerProps) => {
      if (!map || !window.odf) return null;

      const layerId = props.type === "geoserver" ? props.layer : props.id;

      // 기존 ODF 레이어들과 비교
      const existingODFLayers = map.getODFLayers();
      const existingLayer = existingODFLayers.find(
        (odfLayer: { getInitialOption: () => any }) => {
          const initialOption = odfLayer.getInitialOption();
          return (
            props.type === "geoserver" &&
            initialOption?.params?.layer ===
              (props as GeoserverLayerProps).layer
          );
        },
      );

      if (existingLayer) {
        return;
      }
      const { type, params } = convertToLayerParams(props);

      const odfLayer = window.odf.LayerFactory.produce(type, {
        ...params,
      });

      if (props.renderOptions?.style) {
        if (type === "geoserver" && params.service === "wms") {
          const sldStyle = window.odf.StyleFactory.produceSLD(
            props.renderOptions.style,
          );
          odfLayer.setSLD(sldStyle);
        } else {
          const style = window.odf.StyleFactory.produce(
            props.renderOptions.style,
          );
          odfLayer.setStyle(style);
        }
      }

      odfLayer.setMap(map);
      odfLayer.fit();

      console.log(props);

      const newLayer: Layer = {
        id: odfLayer.getODFId(),
        name:
          props.type === "geoserver"
            ? (props as GeoserverLayerProps).name
            : layerId || odfLayer.getODFId(),
        type,
        visible: true,
        opacity: 1,
        zIndex: props.zIndex ?? layers.length,
        odfLayer,
        params,
        info:
          props.type === "geoserver"
            ? (props as GeoserverLayerProps).info
            : {
                lyrId: "",
                lyrNm: "",
              },
      };

      addLayerToStore(newLayer);
      if (odfLayer) return odfLayer.getODFId();
    },
    [map],
  );

  const removeLayer = useCallback(
    (layerId: string) => {
      const layer = layers.find((l) => l.id === layerId);
      if (layer?.odfLayer) {
        map?.removeLayer(layer.odfLayer);
        removeLayerFromStore(layerId);
      }
    },
    [map, layers, removeLayerFromStore],
  );

  const updateStyle = useCallback(
    (layerId: string, style: any) => {
      const layer = layers.find((l) => l.id === layerId);
      if (!layer) return;

      try {
        // 스타일이 문자열인 경우 파싱 시도
        const parsedStyle =
          typeof style === "string" ? JSON.parse(style) : style;

        if (layer.type === "geoserver" && layer.params.service === "wms") {
          const sldStyle = window.odf.StyleFactory.produceSLD(parsedStyle);
          layer.odfLayer.setSLD(sldStyle);
        } else {
          const odfStyle = window.odf.StyleFactory.produce(parsedStyle);
          layer.odfLayer.setStyle(odfStyle);
        }

        // 레이어 상태 업데이트
        updateLayerInStore(layerId, { style: parsedStyle });
      } catch (error) {
        console.error("Failed to update layer style:", error);
      }
    },
    [layers, updateLayerInStore],
  );

  const setVisible = useCallback(
    (layerId: string, visible: boolean) => {
      const layer = layers.find((l) => l.id === layerId);
      if (layer) {
        layer.odfLayer.setVisible(visible);
        updateLayerInStore(layerId, { visible });
      }
    },
    [layers, updateLayerInStore],
  );

  const setZIndex = useCallback(
    (layerId: string, zIndex: number) => {
      const layer = layers.find((l) => l.id === layerId);
      if (layer) {
        map?.setZIndex(layer.odfLayer.getODFId(), zIndex);
        updateLayerInStore(layerId, { zIndex });
      }
    },
    [map, layers, updateLayerInStore],
  );

  const fitToLayer = useCallback(
    (layerId: string): boolean => {
      const layer = layers.find((l) => l.id === layerId);
      if (layer) {
        layer.odfLayer.fit();
        return true;
      }
      return false;
    },
    [layers],
  );

  const setMaxZIndex = useCallback(
    (layerId: string) => {
      const layer = layers.find((l) => l.id === layerId);
      if (layer) {
        map?.setZIndex(layerId, map.getMaxZIndex());
      }
    },
    [layers, map],
  );

  const updateLayerStyle = useCallback(
    (layerId: string, style: any) => {
      if (!map || !window.odf) {
        console.log("map 또는 odf가 없어서 스타일 적용 불가");
        return false;
      }
      // 레이어 찾기
      const layer = layers.find((l) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없음 (ID: ${layerId})`);
        return false;
      }

      try {
        // 스타일이 문자열인 경우 파싱 시도
        const parsedStyle =
          typeof style === "string" ? JSON.parse(style) : style;

        if (layer.type === "geoserver" && layer.params.service === "wms") {
          const sldStyle = window.odf.StyleFactory.produceSLD(parsedStyle);
          layer.odfLayer.setSLD(sldStyle);
        } else {
          layer.odfLayer.setStyle(parsedStyle);
        }

        // 레이어 상태 업데이트
        updateLayerInStore(layerId, { style: parsedStyle });

        return true;
      } catch (error) {
        console.error(`스타일 업데이트 중 오류 (ID: ${layerId}):`, error);
        return false;
      }
    },
    [map, layers, updateLayerInStore],
  );
  /**
   * 레이어에서 피쳐 추가 (ODF 방식)
   *
   * @param layerId - 대상 레이어 ID
   * @param feature - 제거할 피쳐 객체
   * @returns 성공 여부
   */
  const addFeature = useCallback(
    (
      layerId: string,
      feature: any,
      options: AddFeatureOptions = {},
    ): boolean => {
      if (!map || !window.odf) {
        console.error("지도 또는 ODF가 초기화되지 않았습니다.");
        return false;
      }
      // 레이어 찾기
      const layer = layers.find((l) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
        return false;
      }
      let tagetFeature = feature;

      // 벡터 레이어 타입 검증 (공통 상수로 뺄 예정) [최희라], use-layer 훅과 코어 분리필요
      const vectorLayerTypes = ["empty", "geojson", "kml", "csv"];
      if (!vectorLayerTypes.includes(layer.type)) {
        console.error(
          `피쳐 추가는 벡터 레이어에서만 가능합니다. 현재 레이어 타입: ${layer.type}`,
        );
        console.error(`지원되는 타입: ${vectorLayerTypes.join(", ")}`);
        return false;
      }

      try {
        const { srid } = options;
        //수정된 사항
        const mapProjection = new MapProjection(map); //지도좌표계로 변경
        if (srid) tagetFeature = mapProjection.projectGeom(feature, srid);

        // ODF 레이어의 addFeature 메서드 사용
        layer.odfLayer.addFeature(tagetFeature);
        return true;
      } catch (error) {
        console.error(`피쳐 추가 중 오류 발생 (레이어 ID: ${layerId}):`, error);
        return false;
      }
    },
    [map, layers],
  );

  const clearFeatures = useCallback(
    (layerId: string): boolean => {
      const layer = layers.find((l) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
        return false;
      }
      try {
        // ODF 레이어의 addFeature 메서드 사용
        layer.odfLayer.clearFeatures();
        return true;
      } catch (error) {
        console.error(`피쳐 추가 중 오류 발생 (레이어 ID: ${layerId}):`, error);
        return false;
      }
    },
    [map, layers],
  );

  const setOpacity = useCallback(
    (layerId: string, opacity: number) => {
      const layer = layers.find((l) => l.id === layerId);
      if (layer?.odfLayer) {
        layer.odfLayer.setOpacity(opacity);
        updateLayerInStore(layerId, { opacity });
      }
    },
    [layers, updateLayerInStore],
  );

  return {
    layers,
    addLayer,
    removeLayer,
    updateStyle,
    setVisible,
    setZIndex,
    selectedLayerId,
    selectLayer: setSelectedLayer,
    expandedGroups,
    toggleGroup,
    setOpacity,
    setMaxZIndex,
    fitToLayer,
    updateLayerStyle,
    clearFeatures,
    addFeature,
  };
}
