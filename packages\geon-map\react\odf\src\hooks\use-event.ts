import { Event } from "@geon-map/core";
import { useEffect } from "react";

import { useEventStore } from "../stores/event-store";
import { useMapStore } from "../stores/map-store";

// ✅ 마우스 좌표 훅
export const useMouseCoordinate = (enable: boolean) => {
  const map = useMapStore((s) => s.map);
  const odf = useMapStore((s) => s.odf);
  const setMousePosition = useEventStore((s) => s.setMousePosition);
  const setCoordinate = useEventStore((s) => s.setCoordinate);
  const coordinate = useEventStore((s) => s.coordinate);

  useEffect(() => {
    if (!map || !odf) return;

    const eventInstance = Event.getInstance(map, odf);
    eventInstance.setCoordinateCallback(setCoordinate);

    if (enable) {
      setMousePosition(true);
      eventInstance.mousePositionOn();
    }

    return () => {
      setMousePosition(false);
      eventInstance.mousePositionOff();
    };
  }, [map, enable, setMousePosition, setCoordinate]);

  return coordinate;
};
