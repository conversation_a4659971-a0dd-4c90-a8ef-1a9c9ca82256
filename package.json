{"name": "my-turbo", "private": true, "type": "module", "scripts": {"build": "turbo run build", "dev": "pnpm clean:build && turbo run dev", "dev:prod": "turbo run dev:prod", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "check-types": "turbo run check-types", "prepare": "husky", "web": "pnpm --filter web", "storybook": "pnpm --filter web storybook", "release": "turbo run build --filter=./packages/**/* && changeset publish", "clean": "rimraf node_modules/.cache .turbo && rimraf packages/**/dist && rimraf apps/**/.next", "clean:build": "rimraf --glob \"packages/*/*/dist\" \"packages/*/*/*/dist\" apps/*/.next\""}, "devDependencies": {"@changesets/cli": "^2.29.5", "cross-env": "^10.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "rimraf": "^6.0.1", "turbo": "^2.5.5", "typescript": "5.8.3"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748", "engines": {"node": ">=18"}, "dependencies": {"changesets-gitlab": "^0.13.3"}}