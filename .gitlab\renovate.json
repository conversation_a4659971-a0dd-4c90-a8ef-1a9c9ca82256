{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended", ":semanticCommits", ":semanticPrefixFix", ":enableVulnerabilityAlertsWithLabel('security')", "group:monorepos"], "platformAutomerge": true, "dependencyDashboard": true, "packageRules": [{"matchPackageNames": ["fumadocs-*", "fumadocs"], "matchPackagePatterns": ["^fumadocs"], "enabled": false, "description": "Disable automatic updates for fumadocs packages"}, {"matchUpdateTypes": ["patch", "minor"], "groupName": "patch/minor dependencies", "prPriority": 1, "automerge": true}, {"matchUpdateTypes": ["major"], "groupName": "major updates", "prPriority": -1, "automerge": false}], "vulnerabilityAlerts": {"labels": ["security"], "automerge": true}, "lockFileMaintenance": {"enabled": true, "automerge": true, "schedule": ["before 5am every weekday"]}, "timezone": "Asia/Seoul", "prConcurrentLimit": 5, "prHourlyLimit": 5, "labels": ["dependencies"], "baseBranches": ["main"], "separateMinorPatch": false}