// roadview-widget.tsx
"use client";

import { Coordinate } from "@geon-map/core";
import { useMapStore } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Label } from "@geon-ui/react/primitives/label";
import { Switch } from "@geon-ui/react/primitives/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@geon-ui/react/primitives/tooltip";
import { Resizable } from "re-resizable";
import { useEffect, useRef, useState } from "react";

import { useRoadview } from "../../hooks/use-roadview";

const API_KEY = "40f4f6b64e7ede9838110b5197156f44";

interface RoadviewContentProps {
  width: number;
  height: number;
  center: [number, number];
  marker: any;
}
declare global {
  interface Window {
    kakao: any;
    KakaoRoadView: any;
  }
}
export const RoadviewContent = ({
  width,
  height,
  center,
  marker,
}: RoadviewContentProps) => {
  const rvWrapperRef = useRef<HTMLDivElement>(null);
  const rvRef = useRef<any>(null);
  const rvClientRef = useRef<any>(null);
  const map = useMapStore((state) => state.map);
  const odf = useMapStore((state) => state.odf);

  useEffect(() => {
    if (map && odf && rvWrapperRef.current) {
      loadKakaoScript().then(() => {
        initRoadview();
      });
    }
  }, [map, odf]);

  useEffect(() => {
    if (odf && map) {
      loadKakaoScript().then(() => {
        moveRoadview();
      });
    }
  }, [center]);

  const loadKakaoScript = () => {
    return new Promise<void>((resolve) => {
      if (window.kakao?.maps) {
        window.kakao.maps.load(resolve);
        return;
      }

      const script = document.createElement("script");
      script.src = `//dapi.kakao.com/v2/maps/sdk.js?appkey=${API_KEY}&autoload=false`;
      script.onload = () => window.kakao.maps.load(resolve);
      document.head.appendChild(script);
    });
  };

  const to_5179 = (x: number, y: number) => {
    if (odf) {
      const projection4326 = new odf.Projection({ EPSG: "4326" });
      return projection4326.unproject([x, y], "5179");
    }
  };

  const to_4326 = () => {
    if (odf) {
      const projection = map?.getProjection();
      const center4326 = projection.unproject(center, "4326");
      return new window.kakao.maps.LatLng(center4326[1], center4326[0]);
    }
  };

  const moveRoadview = () => {
    const position = to_4326();
    if (!position) return;

    rvClientRef?.current?.getNearestPanoId(position, 50, (panoId: number) => {
      if (!panoId) {
        console.warn("도로가 존재하지 않는 지역입니다.");
      } else {
        rvRef.current.setPanoId(panoId, position);
        rvRef.current.relayout();
      }
    });
  };

  const roadviewEvent = () => {
    window.kakao.maps.event.addListener(
      rvRef.current,
      "position_changed",
      () => {
        if (odf && marker) {
          const rvPosition = rvRef.current.getPosition();
          const xy5179 = to_5179(rvPosition.La, rvPosition.Ma);
          marker.setPosition(new odf.Coordinate(xy5179));
          map?.setCenter(new odf.Coordinate(xy5179));
        }
      },
    );
  };

  const initRoadview = () => {
    if (!rvWrapperRef.current) return;
    rvRef.current = new window.kakao.maps.Roadview(rvWrapperRef.current);
    rvClientRef.current = new window.kakao.maps.RoadviewClient();
    roadviewEvent();
    moveRoadview();
  };

  return (
    <div
      ref={rvWrapperRef}
      style={{ width: width - 20, height: height - 60 }}
      className="mt-2 border rounded w-[400px] h-[300px] overflow-hidden"
    />
  );
};

export const RoadviewTrigger = ({
  className,
  children,
  onMarkerSelect,
  marker,
  onResize,
  onCenterSelect,
}: {
  className?: string;
  children: (enabled: boolean) => React.ReactNode;
  onMarkerSelect: (marker: any) => void;
  marker: any;
  onResize?: (size: { width: number; height: number }) => void;
  onCenterSelect?: (center: [number, number]) => void;
}) => {
  const {
    enabled,
    resizableWidth,
    setResizableWidth,
    setPrevWidth,
    handleToggle,
  } = useRoadview({ onMarkerSelect, onCenterSelect });

  return (
    <Resizable
      size={{ width: resizableWidth, height: "auto" }}
      style={{ position: "absolute" }}
      minWidth={160}
      maxWidth={1000}
      maxHeight={640}
      enable={{
        top: true,
        right: true,
        bottom: true,
        left: true,
        topRight: true,
        bottomRight: true,
        bottomLeft: true,
        topLeft: true,
      }}
      onResizeStop={(_, __, ref) => {
        const width = ref.offsetWidth;
        const height = ref.offsetHeight;
        setResizableWidth(width);
        if (enabled) setPrevWidth(width);
        onResize?.({ width, height });
      }}
      className={cn(
        "absolute right-4 bottom-40 z-[100] rounded-lg border p-4 shadow-sm bg-white text-sm space-y-3",
        className,
      )}
    >
      <div className="flex items-center gap-3">
        <Label htmlFor="roadview-switch" className="text-sm font-medium">
          로드뷰 표시
        </Label>
        <Switch
          id="roadview-switch"
          checked={enabled}
          onCheckedChange={(checked) => handleToggle(checked, marker)}
        />
        {enabled && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="text-[13px] text-gray-700 cursor-help truncate max-w-[150px]">
                  로드뷰를 조작 후 방향키, 스페이스 바를 이용하여 조작이
                  가능합니다.
                </div>
              </TooltipTrigger>
              <TooltipContent
                side="top"
                sideOffset={9}
                className="text-[13px] max-w-[250px]"
              >
                로드뷰를 조작 후 방향키, 스페이스 바를 이용하여 조작이
                가능합니다.
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
      {children(enabled)}
    </Resizable>
  );
};

export const RoadviewWidget = () => {
  const [size, setSize] = useState({ width: 420, height: 300 });
  const [center, setCenter] = useState<Coordinate>([0, 0]);
  const [marker, setMarker] = useState<any>(null);

  return (
    <RoadviewTrigger
      onResize={setSize}
      onCenterSelect={setCenter}
      onMarkerSelect={setMarker}
      marker={marker}
    >
      {(enabled) =>
        enabled &&
        center[0] > 0 && (
          <RoadviewContent
            width={size.width}
            height={size.height}
            center={center}
            marker={marker}
          />
        )
      }
    </RoadviewTrigger>
  );
};
